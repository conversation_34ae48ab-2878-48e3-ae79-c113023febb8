using AirMonitor.Helpers;
using System.ComponentModel;

namespace AirMonitor.Models;

/// <summary>
/// 数据包协议类型枚举
/// </summary>
public enum DataPacketProtocolType
{
    /// <summary>
    /// 商用内外机通讯协议（头码：0x7E，ModbusCRC16校验）
    /// </summary>
    [Description("商用内外机通讯协议")]
    CommercialProtocol = 0,

    /// <summary>
    /// 模块机通讯协议（头码：0x5B，CCITT-CRC16校验）
    /// </summary>
    [Description("模块机通讯协议")]
    ModuleProtocol = 1,

    /// <summary>
    /// 自动识别协议类型
    /// </summary>
    [Description("自动识别")]
    AutoDetect = 2
}

/// <summary>
/// CRC校验类型枚举
/// </summary>
public enum CrcType
{
    /// <summary>
    /// Modbus CRC16校验
    /// </summary>
    ModbusCrc16,

    /// <summary>
    /// CCITT CRC16校验
    /// </summary>
    CcittCrc16
}

/// <summary>
/// 数据包解析状态枚举
/// </summary>
public enum DataPacketParseStatus
{
    /// <summary>
    /// 等待头码
    /// </summary>
    WaitingForHeader,

    /// <summary>
    /// 等待数据
    /// </summary>
    WaitingForData,

    /// <summary>
    /// 解析完成
    /// </summary>
    ParseCompleted,

    /// <summary>
    /// 解析错误
    /// </summary>
    ParseError
}

/// <summary>
/// 数据包基类
/// </summary>
public abstract class DataPacketBase
{
    /// <summary>
    /// 协议类型
    /// </summary>
    public DataPacketProtocolType ProtocolType { get; set; }

    /// <summary>
    /// 头码
    /// </summary>
    public byte Header { get; set; }

    /// <summary>
    /// 原始数据
    /// </summary>
    public byte[] RawData { get; set; } = Array.Empty<byte>();

    /// <summary>
    /// 接收时间
    /// </summary>
    public DateTime ReceivedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// CRC校验是否通过
    /// </summary>
    public bool IsCrcValid { get; set; }

    /// <summary>
    /// 计算的CRC值
    /// </summary>
    public ushort CalculatedCrc { get; set; }

    /// <summary>
    /// 接收到的CRC值
    /// </summary>
    public ushort ReceivedCrc { get; set; }

    /// <summary>
    /// 验证CRC校验
    /// </summary>
    /// <returns>校验是否通过</returns>
    public abstract bool ValidateCrc();

    /// <summary>
    /// 获取数据字节数组
    /// </summary>
    /// <returns>数据字节数组</returns>
    public abstract byte[] GetDataBytes();
}

/// <summary>
/// 商用内外机通讯协议数据包
/// 帧结构：头码(1) + 源地址(1) + 目标地址(1) + 命令码(1) + 报文长度(1) + 数据字(N) + ModbusCRC16(2)
/// </summary>
public class CommercialProtocolPacket : DataPacketBase
{
    /// <summary>
    /// 源地址
    /// </summary>
    public byte SourceAddress { get; set; }

    /// <summary>
    /// 目标地址
    /// </summary>
    public byte TargetAddress { get; set; }

    /// <summary>
    /// 命令码
    /// </summary>
    public byte CommandCode { get; set; }

    /// <summary>
    /// 报文长度（从头码到CRC校验结束的总长度）
    /// </summary>
    public byte MessageLength { get; set; }

    /// <summary>
    /// 数据字
    /// </summary>
    public byte[] DataBytes { get; set; } = Array.Empty<byte>();

    public CommercialProtocolPacket()
    {
        ProtocolType = DataPacketProtocolType.CommercialProtocol;
        Header = 0x7E;
    }

    public override bool ValidateCrc()
    {
        if (RawData.Length < 7) // 最小长度：头码+源地址+目标地址+命令码+长度+CRC(2字节)
            return false;

        // 计算CRC的数据范围：从头码到数据字结束（不包括CRC本身）
        var dataForCrc = new byte[RawData.Length - 2];
        Array.Copy(RawData, 0, dataForCrc, 0, dataForCrc.Length);

        CalculatedCrc = CrcHelper.CalculateModbusCrc16(dataForCrc);
        ReceivedCrc = (ushort)((RawData[RawData.Length - 2] << 8) | RawData[RawData.Length - 1]);

        IsCrcValid = CalculatedCrc == ReceivedCrc;
        return IsCrcValid;
    }

    public override byte[] GetDataBytes()
    {
        return DataBytes;
    }
}

/// <summary>
/// 模块机通讯协议数据包
/// 帧结构：头码(1) + 地址(1) + 命令码(1) + 报文长度(1) + 数据字(N) + CCITT-CRC16(2)
/// </summary>
public class ModuleProtocolPacket : DataPacketBase
{
    /// <summary>
    /// 地址
    /// </summary>
    public byte Address { get; set; }

    /// <summary>
    /// 命令码
    /// </summary>
    public byte CommandCode { get; set; }

    /// <summary>
    /// 报文长度（除CRC校验外的数据长度）
    /// </summary>
    public byte MessageLength { get; set; }

    /// <summary>
    /// 数据字
    /// </summary>
    public byte[] DataBytes { get; set; } = Array.Empty<byte>();

    public ModuleProtocolPacket()
    {
        ProtocolType = DataPacketProtocolType.ModuleProtocol;
        Header = 0x5B;
    }

    public override bool ValidateCrc()
    {
        if (RawData.Length < 6) // 最小长度：头码+地址+命令码+长度+CRC(2字节)
            return false;

        // 计算CRC的数据范围：从头码到数据字结束（不包括CRC本身）
        var dataForCrc = new byte[RawData.Length - 2];
        Array.Copy(RawData, 0, dataForCrc, 0, dataForCrc.Length);

        CalculatedCrc = CrcHelper.CalculateCcittCrc16(dataForCrc);
        ReceivedCrc = (ushort)((RawData[RawData.Length - 2] << 8) | RawData[RawData.Length - 1]);

        IsCrcValid = CalculatedCrc == ReceivedCrc;
        return IsCrcValid;
    }

    public override byte[] GetDataBytes()
    {
        return DataBytes;
    }
}

/// <summary>
/// 数据包解析结果
/// </summary>
public class DataPacketParseResult
{
    /// <summary>
    /// 解析状态
    /// </summary>
    public DataPacketParseStatus Status { get; set; }

    /// <summary>
    /// 解析出的数据包
    /// </summary>
    public DataPacketBase? Packet { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 已处理的字节数
    /// </summary>
    public int ProcessedBytes { get; set; }

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess => Status == DataPacketParseStatus.ParseCompleted && Packet != null;
}

/// <summary>
/// 数据包解析事件参数
/// </summary>
public class DataPacketParsedEventArgs : EventArgs
{
    /// <summary>
    /// 解析结果
    /// </summary>
    public DataPacketParseResult Result { get; set; } = new();

    /// <summary>
    /// 源端口名称
    /// </summary>
    public string PortName { get; set; } = string.Empty;
}

/// <summary>
/// 数据包解析错误事件参数
/// </summary>
public class DataPacketParseErrorEventArgs : EventArgs
{
    /// <summary>
    /// 错误消息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// 异常信息
    /// </summary>
    public Exception? Exception { get; set; }

    /// <summary>
    /// 源端口名称
    /// </summary>
    public string PortName { get; set; } = string.Empty;

    /// <summary>
    /// 错误发生时的原始数据
    /// </summary>
    public byte[] RawData { get; set; } = Array.Empty<byte>();
}
