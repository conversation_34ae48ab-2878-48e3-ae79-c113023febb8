using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using AirMonitor.Services;

namespace AirMonitor.ViewModels;

/// <summary>
/// 主窗口ViewModel
/// </summary>
public partial class MainViewModel : ViewModelBase
{
    private readonly IConfigurationService _configurationService;
    private readonly IDialogService _dialogService;

    [ObservableProperty]
    private string _statusMessage = "就绪";

    [ObservableProperty]
    private string _applicationVersion = "1.0.0";

    [ObservableProperty]
    private string _applicationName = "AirMonitor";

    [ObservableProperty]
    private bool _isToolsMenuEnabled = true;

    [ObservableProperty]
    private bool _isMainContentVisible = true;

    /// <summary>
    /// 串口连接命令
    /// </summary>
    public ICommand SerialPortConnectionCommand { get; }

    /// <summary>
    /// 帧数据监听命令
    /// </summary>
    public ICommand FrameDataListenerCommand { get; }

    /// <summary>
    /// 工具菜单命令
    /// </summary>
    public ICommand ToolsMenuCommand { get; }

    /// <summary>
    /// 帮助菜单命令
    /// </summary>
    public ICommand HelpMenuCommand { get; }

    /// <summary>
    /// 关于命令
    /// </summary>
    public ICommand AboutCommand { get; }

    /// <summary>
    /// 退出命令
    /// </summary>
    public ICommand ExitCommand { get; }

    public MainViewModel(IConfigurationService configurationService, ILoggingService loggingService, IDialogService dialogService)
        : base(loggingService)
    {
        _configurationService = configurationService ?? throw new ArgumentNullException(nameof(configurationService));
        _dialogService = dialogService ?? throw new ArgumentNullException(nameof(dialogService));

        Title = "AirMonitor";
        ApplicationName = _configurationService.AppSettings.Application.Name;

        // 初始化命令
        SerialPortConnectionCommand = new RelayCommand(ShowSerialPortConnection);
        FrameDataListenerCommand = new RelayCommand(ShowFrameDataListener);
        ToolsMenuCommand = new RelayCommand(ShowToolsMenu);
        HelpMenuCommand = new RelayCommand(ShowHelpMenu);
        AboutCommand = new RelayCommand(ShowAbout);
        ExitCommand = new RelayCommand(Exit);
    }

    /// <summary>
    /// 初始化
    /// </summary>
    protected override async Task OnInitializeAsync()
    {
        await ExecuteAsync(async () =>
        {
            LoggingService?.LogInformation("正在初始化主窗口...");

            // 加载配置
            await _configurationService.LoadConfigurationAsync();

            // 更新应用程序信息
            ApplicationVersion = _configurationService.AppSettings.Application.Version;
            ApplicationName = _configurationService.AppSettings.Application.Name;
            Title = ApplicationName;

            StatusMessage = "就绪";

            LoggingService?.LogInformation("主窗口初始化完成");
        });
    }

    /// <summary>
    /// 显示串口连接对话框
    /// </summary>
    private void ShowSerialPortConnection()
    {
        LoggingService?.LogInformation("串口连接菜单被点击");
        StatusMessage = "正在打开串口连接对话框...";

        try
        {
            var result = _dialogService.ShowDialog<Views.SerialPortConnectionDialog>();

            if (result == true)
            {
                StatusMessage = "串口连接配置已保存";
                LoggingService?.LogInformation("用户确认了串口连接配置");
            }
            else
            {
                StatusMessage = "已取消串口连接配置";
                LoggingService?.LogInformation("用户取消了串口连接配置");
            }
        }
        catch (Exception ex)
        {
            LoggingService?.LogError(ex, "显示串口连接对话框失败");
            StatusMessage = "打开串口连接对话框失败";
        }
    }

    /// <summary>
    /// 显示帧数据监听窗体
    /// </summary>
    private async void ShowFrameDataListener()
    {
        try
        {
            LoggingService?.LogInformation("显示帧数据监听窗体");
            StatusMessage = "正在打开帧数据监听窗体...";

            // 使用对话框服务显示独立窗体（非模态）
            _dialogService.ShowWindow(() =>
            {
                var window = new Views.FrameDataListenerWindow();
                return window;
            });

            StatusMessage = "帧数据监听窗体已打开";
            LoggingService?.LogInformation("帧数据监听窗体已打开");
        }
        catch (Exception ex)
        {
            await OnErrorAsync(ex, "显示帧数据监听窗体");
        }
    }

    /// <summary>
    /// 显示工具菜单
    /// </summary>
    private void ShowToolsMenu()
    {
        LoggingService?.LogInformation("工具菜单被点击");

       
    }

    /// <summary>
    /// 显示帮助菜单
    /// </summary>
    private void ShowHelpMenu()
    {
        LoggingService?.LogInformation("帮助菜单被点击");
        StatusMessage = "帮助菜单功能待实现";
    }

    /// <summary>
    /// 显示关于信息
    /// </summary>
    private void ShowAbout()
    {
        LoggingService?.LogInformation("显示关于信息");

        var aboutMessage = $"""
            {ApplicationName}
            版本: {ApplicationVersion}

            这是一个基于WPF和MVVM模式的桌面应用程序。

            技术架构:
            • 依赖注入 (Microsoft.Extensions.DependencyInjection)
            • MVVM架构 (CommunityToolkit.Mvvm)
            • 日志系统 (Serilog)
            • 配置管理 (Microsoft.Extensions.Configuration)
            • 视图模型自动绑定
            • 模态对话框服务

            界面结构:
            • 标准菜单栏和工具栏
            • 响应式主内容区域
            • 实时状态栏显示
            """;

        System.Windows.MessageBox.Show(
            aboutMessage,
            "关于",
            System.Windows.MessageBoxButton.OK,
            System.Windows.MessageBoxImage.Information);
    }

    /// <summary>
    /// 退出应用程序
    /// </summary>
    private void Exit()
    {
        LoggingService?.LogInformation("用户请求退出应用程序");
        StatusMessage = "正在退出...";

        // 这里可以添加退出前的清理逻辑
        System.Windows.Application.Current.Shutdown();
    }

    /// <summary>
    /// 处理错误
    /// </summary>
    protected override async Task OnErrorAsync(Exception exception, string operationName)
    {
        await base.OnErrorAsync(exception, operationName);

        StatusMessage = $"操作失败: {operationName}";

        // 显示错误消息给用户
        System.Windows.MessageBox.Show(
            $"操作 '{operationName}' 执行失败:\n{exception.Message}",
            "错误",
            System.Windows.MessageBoxButton.OK,
            System.Windows.MessageBoxImage.Error);
    }
}
