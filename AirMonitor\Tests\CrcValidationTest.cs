using AirMonitor.Models;
using AirMonitor.Helpers;

namespace AirMonitor.Tests;

/// <summary>
/// CRC校验测试类
/// 用于验证商用协议和模块协议的CRC校验实现
/// </summary>
public static class CrcValidationTest
{
    /// <summary>
    /// 测试商用协议CRC校验（排除头码）
    /// </summary>
    public static void TestCommercialProtocolCrc()
    {
        Console.WriteLine("=== 商用协议CRC校验测试 ===");

        // 构造一个测试数据包：头码 + 源地址 + 目标地址 + 命令码 + 长度 + 数据 + CRC
        // 头码: 0x7E (不参与CRC计算)
        // 源地址: 0x01
        // 目标地址: 0x02  
        // 命令码: 0x03
        // 长度: 0x09 (总长度9字节)
        // 数据: 0x04, 0x05
        // CRC: 需要计算

        var dataForCrc = new byte[] { 0x01, 0x02, 0x03, 0x09, 0x04, 0x05 }; // 不包含头码
        var calculatedCrc = CrcHelper.CalculateModbusCrc16(dataForCrc);
        
        Console.WriteLine($"CRC计算数据: {string.Join(" ", dataForCrc.Select(b => $"0x{b:X2}"))}");
        Console.WriteLine($"计算得到的CRC: 0x{calculatedCrc:X4}");

        // 构造完整数据包
        var fullPacket = new byte[9];
        fullPacket[0] = 0x7E; // 头码
        Array.Copy(dataForCrc, 0, fullPacket, 1, dataForCrc.Length);
        fullPacket[7] = (byte)(calculatedCrc >> 8);   // CRC高字节
        fullPacket[8] = (byte)(calculatedCrc & 0xFF); // CRC低字节

        Console.WriteLine($"完整数据包: {string.Join(" ", fullPacket.Select(b => $"0x{b:X2}"))}");

        // 创建商用协议数据包对象并验证
        var packet = new CommercialProtocolPacket
        {
            RawData = fullPacket,
            Header = fullPacket[0],
            SourceAddress = fullPacket[1],
            TargetAddress = fullPacket[2],
            CommandCode = fullPacket[3],
            MessageLength = fullPacket[4],
            DataBytes = new byte[] { fullPacket[5], fullPacket[6] }
        };

        var isValid = packet.ValidateCrc();
        Console.WriteLine($"CRC校验结果: {(isValid ? "通过" : "失败")}");
        Console.WriteLine($"计算的CRC: 0x{packet.CalculatedCrc:X4}");
        Console.WriteLine($"接收的CRC: 0x{packet.ReceivedCrc:X4}");
        Console.WriteLine();
    }

    /// <summary>
    /// 测试模块协议CRC校验（包含头码）
    /// </summary>
    public static void TestModuleProtocolCrc()
    {
        Console.WriteLine("=== 模块协议CRC校验测试 ===");

        // 构造一个测试数据包：头码 + 地址 + 命令码 + 长度 + 数据 + CRC
        // 头码: 0x5B (参与CRC计算)
        // 地址: 0x01
        // 命令码: 0x02
        // 长度: 0x06 (除CRC外的数据长度)
        // 数据: 0x03, 0x04
        // CRC: 需要计算

        var dataForCrc = new byte[] { 0x5B, 0x01, 0x02, 0x06, 0x03, 0x04 }; // 包含头码
        var calculatedCrc = CrcHelper.CalculateCcittCrc16(dataForCrc);
        
        Console.WriteLine($"CRC计算数据: {string.Join(" ", dataForCrc.Select(b => $"0x{b:X2}"))}");
        Console.WriteLine($"计算得到的CRC: 0x{calculatedCrc:X4}");

        // 构造完整数据包
        var fullPacket = new byte[8];
        Array.Copy(dataForCrc, 0, fullPacket, 0, dataForCrc.Length);
        fullPacket[6] = (byte)(calculatedCrc >> 8);   // CRC高字节
        fullPacket[7] = (byte)(calculatedCrc & 0xFF); // CRC低字节

        Console.WriteLine($"完整数据包: {string.Join(" ", fullPacket.Select(b => $"0x{b:X2}"))}");

        // 创建模块协议数据包对象并验证
        var packet = new ModuleProtocolPacket
        {
            RawData = fullPacket,
            Header = fullPacket[0],
            Address = fullPacket[1],
            CommandCode = fullPacket[2],
            MessageLength = fullPacket[3],
            DataBytes = new byte[] { fullPacket[4], fullPacket[5] }
        };

        var isValid = packet.ValidateCrc();
        Console.WriteLine($"CRC校验结果: {(isValid ? "通过" : "失败")}");
        Console.WriteLine($"计算的CRC: 0x{packet.CalculatedCrc:X4}");
        Console.WriteLine($"接收的CRC: 0x{packet.ReceivedCrc:X4}");
        Console.WriteLine();
    }

    /// <summary>
    /// 运行所有测试
    /// </summary>
    public static void RunAllTests()
    {
        Console.WriteLine("开始CRC校验测试...\n");
        
        TestCommercialProtocolCrc();
        TestModuleProtocolCrc();
        
        Console.WriteLine("CRC校验测试完成。");
    }
}
