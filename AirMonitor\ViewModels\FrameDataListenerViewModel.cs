using System.Collections.ObjectModel;
using System.Collections.Specialized;
using System.IO;
using System.Windows;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using AirMonitor.Models;
using AirMonitor.Services;
using Microsoft.Win32;

namespace AirMonitor.ViewModels;

/// <summary>
/// 帧数据监听窗体ViewModel
/// </summary>
public partial class FrameDataListenerViewModel : ViewModelBase, IDisposable
{
    #region 字段

    private readonly IDataPacketService _dataPacketService;
    private bool _disposed = false;

    [ObservableProperty]
    private string _statusMessage = "就绪";

    [ObservableProperty]
    private bool _isPaused = false;

    [ObservableProperty]
    private bool _autoScrollToBottom = true;

    [ObservableProperty]
    private int _maxDisplayCount = 1000;

    [ObservableProperty]
    private int _totalFrameCount = 0;

    [ObservableProperty]
    private int _displayedFrameCount = 0;

    [ObservableProperty]
    private string _pauseResumeButtonText = "暂停";

    #endregion

    #region 属性

    /// <summary>
    /// 帧数据项集合
    /// </summary>
    public ObservableCollection<FrameDataItem> FrameDataItems { get; } = new();

    /// <summary>
    /// 选中的项目集合
    /// </summary>
    public ObservableCollection<FrameDataItem> SelectedItems { get; } = new();

    #endregion

    #region 命令

    /// <summary>
    /// 暂停/继续命令
    /// </summary>
    public ICommand PauseResumeCommand { get; }

    /// <summary>
    /// 复制选中命令
    /// </summary>
    public ICommand CopySelectedCommand { get; }

    /// <summary>
    /// 导出命令
    /// </summary>
    public ICommand ExportCommand { get; }

    /// <summary>
    /// 清空命令
    /// </summary>
    public ICommand ClearCommand { get; }

    #endregion

    #region 构造函数

    public FrameDataListenerViewModel(
        IDataPacketService dataPacketService,
        ILoggingService loggingService) 
        : base(loggingService)
    {
        _dataPacketService = dataPacketService ?? throw new ArgumentNullException(nameof(dataPacketService));

        Title = "帧数据监听";

        // 初始化命令
        PauseResumeCommand = new RelayCommand(PauseResume);
        CopySelectedCommand = new RelayCommand(CopySelected, CanCopySelected);
        ExportCommand = new RelayCommand(ExportData, CanExportData);
        ClearCommand = new RelayCommand(ClearData, CanClearData);

        // 监听属性变化
        PropertyChanged += OnPropertyChanged;
        SelectedItems.CollectionChanged += OnSelectedItemsChanged;
    }

    #endregion

    #region 初始化和清理

    protected override async Task OnInitializeAsync()
    {
        await base.OnInitializeAsync();

        try
        {
            // 订阅数据分包器事件
            _dataPacketService.PacketParsed += OnPacketParsed;
            _dataPacketService.ParseError += OnParseError;

            StatusMessage = "已开始监听帧数据";
            LoggingService?.LogInformation("帧数据监听已启动");
        }
        catch (Exception ex)
        {
            await OnErrorAsync(ex, "初始化帧数据监听");
        }
    }

    public new void Dispose()
    {
        if (_disposed) return;

        try
        {
            // 取消事件订阅
            _dataPacketService.PacketParsed -= OnPacketParsed;
            _dataPacketService.ParseError -= OnParseError;

            LoggingService?.LogInformation("帧数据监听已停止");
        }
        catch (Exception ex)
        {
            LoggingService?.LogError(ex, "释放帧数据监听资源时发生错误");
        }
        finally
        {
            _disposed = true;
        }
    }

    #endregion

    #region 事件处理

    /// <summary>
    /// 数据包解析完成事件处理
    /// </summary>
    private void OnPacketParsed(object? sender, DataPacketParsedEventArgs e)
    {
        if (_disposed || IsPaused || e.Result?.Packet == null) return;

        try
        {
            // 在UI线程中更新数据
            Application.Current.Dispatcher.BeginInvoke(() =>
            {
                var frameItem = FrameDataItem.FromDataPacket(e.Result.Packet, e.PortName);
                
                // 添加到集合
                FrameDataItems.Add(frameItem);
                TotalFrameCount++;

                // 控制显示数量
                while (FrameDataItems.Count > MaxDisplayCount)
                {
                    FrameDataItems.RemoveAt(0);
                }

                DisplayedFrameCount = FrameDataItems.Count;

                // 更新命令状态
                UpdateCommandStates();

                StatusMessage = $"已接收 {TotalFrameCount} 帧数据";
            });
        }
        catch (Exception ex)
        {
            LoggingService?.LogError(ex, "处理数据包时发生错误");
        }
    }

    /// <summary>
    /// 数据包解析错误事件处理
    /// </summary>
    private void OnParseError(object? sender, DataPacketParseErrorEventArgs e)
    {
        if (_disposed) return;

        try
        {
            Application.Current.Dispatcher.BeginInvoke(() =>
            {
                StatusMessage = $"数据解析错误: {e.ErrorMessage}";
            });

            LoggingService?.LogWarning("数据包解析错误: {ErrorMessage}, 端口: {PortName}", 
                e.ErrorMessage, e.PortName);
        }
        catch (Exception ex)
        {
            LoggingService?.LogError(ex, "处理解析错误时发生异常");
        }
    }

    /// <summary>
    /// 属性变化事件处理
    /// </summary>
    private void OnPropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
    {
        if (e.PropertyName == nameof(IsPaused))
        {
            PauseResumeButtonText = IsPaused ? "继续" : "暂停";
            StatusMessage = IsPaused ? "监听已暂停" : "正在监听帧数据";
        }
    }

    /// <summary>
    /// 选中项变化事件处理
    /// </summary>
    private void OnSelectedItemsChanged(object? sender, NotifyCollectionChangedEventArgs e)
    {
        // 更新命令的可执行状态
        UpdateCommandStates();
    }

    /// <summary>
    /// 更新命令的可执行状态
    /// </summary>
    private void UpdateCommandStates()
    {
        ((RelayCommand)CopySelectedCommand).NotifyCanExecuteChanged();
        ((RelayCommand)ExportCommand).NotifyCanExecuteChanged();
        ((RelayCommand)ClearCommand).NotifyCanExecuteChanged();
    }

    #endregion

    #region 命令实现

    /// <summary>
    /// 暂停/继续
    /// </summary>
    private void PauseResume()
    {
        IsPaused = !IsPaused;
        LoggingService?.LogInformation("帧数据监听{Status}", IsPaused ? "已暂停" : "已继续");
    }

    /// <summary>
    /// 复制选中的数据
    /// </summary>
    private void CopySelected()
    {
        try
        {
            if (SelectedItems.Count == 0) return;

            var text = string.Join(Environment.NewLine, 
                SelectedItems.Select(item => item.FullText));

            Clipboard.SetText(text);
            StatusMessage = $"已复制 {SelectedItems.Count} 条数据到剪贴板";
            LoggingService?.LogInformation("已复制 {Count} 条帧数据到剪贴板", SelectedItems.Count);
        }
        catch (Exception ex)
        {
            LoggingService?.LogError(ex, "复制数据到剪贴板失败");
            StatusMessage = "复制数据失败";
        }
    }

    /// <summary>
    /// 是否可以复制选中的数据
    /// </summary>
    private bool CanCopySelected()
    {
        return SelectedItems.Count > 0;
    }

    /// <summary>
    /// 导出数据
    /// </summary>
    private async void ExportData()
    {
        try
        {
            var saveFileDialog = new SaveFileDialog
            {
                Title = "导出帧数据",
                Filter = "文本文件 (*.txt)|*.txt|所有文件 (*.*)|*.*",
                DefaultExt = "txt",
                FileName = $"FrameData_{DateTime.Now:yyyyMMdd_HHmmss}.txt"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                var lines = FrameDataItems.Select(item => item.FullText);
                await File.WriteAllLinesAsync(saveFileDialog.FileName, lines);

                StatusMessage = $"已导出 {FrameDataItems.Count} 条数据到文件";
                LoggingService?.LogInformation("已导出 {Count} 条帧数据到文件: {FileName}", 
                    FrameDataItems.Count, saveFileDialog.FileName);
            }
        }
        catch (Exception ex)
        {
            await OnErrorAsync(ex, "导出帧数据");
        }
    }

    /// <summary>
    /// 是否可以导出数据
    /// </summary>
    private bool CanExportData()
    {
        return FrameDataItems.Count > 0;
    }

    /// <summary>
    /// 清空数据
    /// </summary>
    private void ClearData()
    {
        try
        {
            var count = FrameDataItems.Count;
            FrameDataItems.Clear();
            SelectedItems.Clear();
            DisplayedFrameCount = 0;
            // 注意：不清空TotalFrameCount，保持累计计数

            // 更新命令状态
            UpdateCommandStates();

            StatusMessage = $"已清空 {count} 条显示数据";
            LoggingService?.LogInformation("已清空 {Count} 条帧数据显示", count);
        }
        catch (Exception ex)
        {
            LoggingService?.LogError(ex, "清空数据失败");
            StatusMessage = "清空数据失败";
        }
    }

    /// <summary>
    /// 是否可以清空数据
    /// </summary>
    private bool CanClearData()
    {
        return FrameDataItems.Count > 0;
    }

    #endregion
}
